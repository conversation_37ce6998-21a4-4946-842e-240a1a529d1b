package com.sgk.foodiq.service

import android.graphics.Bitmap
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class OCRService {
    
    private val textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    
    suspend fun extractTextFromImage(bitmap: Bitmap): String {
        if (bitmap.isRecycled) {
            throw Exception("Image is corrupted. Please try taking another photo.")
        }

        return suspendCancellableCoroutine { continuation ->
            try {
                val image = InputImage.fromBitmap(bitmap, 0)

                textRecognizer.process(image)
                    .addOnSuccessListener { visionText ->
                        val extractedText = visionText.text
                        if (extractedText.isBlank()) {
                            continuation.resumeWithException(
                                Exception("No text detected in image. Please ensure the nutrition label is clearly visible and try again.")
                            )
                        } else {
                            continuation.resume(extractedText)
                        }
                    }
                    .addOnFailureListener { exception ->
                        continuation.resumeWithException(
                            Exception("Failed to process image: ${exception.message}")
                        )
                    }
            } catch (e: Exception) {
                continuation.resumeWithException(
                    Exception("Error processing image: ${e.message}")
                )
            }
        }
    }
    
    fun processNutritionText(rawText: String): String {
        // Clean and format the extracted text for better API processing
        val lines = rawText.split("\n")
        val nutritionKeywords = listOf(
            "nutrition", "facts", "ingredients", "calories", "fat", "sodium", 
            "carbohydrate", "protein", "sugar", "fiber", "vitamin", "mineral",
            "serving", "amount", "daily", "value", "%", "mg", "g", "kcal"
        )
        
        val relevantLines = lines.filter { line ->
            val lowerLine = line.lowercase().trim()
            lowerLine.isNotEmpty() && (
                nutritionKeywords.any { keyword -> lowerLine.contains(keyword) } ||
                lowerLine.any { it.isDigit() } ||
                lowerLine.contains("%") ||
                lowerLine.length > 3
            )
        }
        
        return relevantLines.joinToString("\n").trim()
    }
}
