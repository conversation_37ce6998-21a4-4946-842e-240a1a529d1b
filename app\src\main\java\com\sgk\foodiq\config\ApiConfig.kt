package com.sgk.foodiq.config

object ApiConfig {
    // TODO: Replace with your actual Gemini API key
    // Get your API key from: https://makersuite.google.com/app/apikey
    const val GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"
    
    // For production apps, consider storing the API key in:
    // 1. BuildConfig (configured in build.gradle)
    // 2. Local properties file (not committed to version control)
    // 3. Secure storage or environment variables
    // 4. Remote configuration service
}
