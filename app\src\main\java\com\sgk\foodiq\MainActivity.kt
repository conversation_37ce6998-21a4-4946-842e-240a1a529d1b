package com.sgk.foodiq

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.sgk.foodiq.ui.camera.CameraScreen
import com.sgk.foodiq.ui.results.ResultsScreen
import com.sgk.foodiq.ui.theme.FoodIQTheme
import com.sgk.foodiq.viewmodel.FoodIQViewModel
import com.sgk.foodiq.viewmodel.UiState

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            FoodIQTheme {
                FoodIQApp()
            }
        }
    }
}

@Composable
fun FoodIQApp(
    viewModel: FoodIQViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    when (uiState) {
        is UiState.Camera -> {
            CameraScreen(
                onImageCaptured = { bitmap ->
                    viewModel.processImage(bitmap)
                }
            )
        }

        is UiState.Processing -> {
            ProcessingScreen()
        }

        is UiState.Results -> {
            ResultsScreen(
                analysis = uiState.analysis,
                onBackClick = {
                    viewModel.resetToCamera()
                }
            )
        }

        is UiState.Error -> {
            ErrorScreen(
                message = uiState.message,
                onRetry = {
                    viewModel.retryFromError()
                }
            )
        }
    }
}

@Composable
private fun ProcessingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp)
            )
            Text(
                text = "Analyzing nutrition label...",
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = "This may take a few seconds",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
private fun ErrorScreen(
    message: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.padding(32.dp)
        ) {
            Text(
                text = "Oops! Something went wrong",
                style = MaterialTheme.typography.headlineSmall
            )
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            Button(onClick = onRetry) {
                Text("Try Again")
            }
        }
    }
}