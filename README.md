# FoodIQ - Nutrition Label Scanner

FoodIQ is an Android app that uses camera functionality and AI to analyze nutrition labels. Simply point your camera at a nutrition label, and the app will:

- Extract text using O<PERSON> (Google ML Kit)
- Analyze the nutrition information using Google's Gemini AI
- Provide a health rating (1-10 scale)
- Categorize ingredients as good, bad, or neutral with explanations

## Features

- **Camera Integration**: Uses CameraX for smooth camera preview and image capture
- **OCR Text Recognition**: Google ML Kit for accurate text extraction from nutrition labels
- **AI Analysis**: Google Gemini AI for intelligent nutrition analysis
- **Intuitive UI**: Clean, modern interface built with Jetpack Compose
- **Real-time Processing**: Fast analysis with loading indicators and error handling

## Setup Instructions

### Prerequisites

1. Android Studio (latest version recommended)
2. Android device or emulator with API level 24+
3. Google Gemini API key

### Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd FoodIQ
   ```

2. **Get a Gemini API Key**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the API key

3. **Configure the API Key**
   - Open `app/src/main/java/com/sgk/foodiq/config/ApiConfig.kt`
   - Replace `YOUR_GEMINI_API_KEY_HERE` with your actual API key:
   ```kotlin
   const val GEMINI_API_KEY = "your_actual_api_key_here"
   ```

4. **Build and Run**
   - Open the project in Android Studio
   - Sync the project with Gradle files
   - Run the app on your device or emulator

### Permissions

The app requires the following permissions:
- **Camera**: To capture nutrition label images
- **Internet**: To communicate with the Gemini AI API

These permissions are automatically requested when needed.

## How to Use

1. **Launch the app** - You'll see the camera screen
2. **Grant permissions** - Allow camera and internet access when prompted
3. **Position the nutrition label** - Frame the nutrition label within the camera view
4. **Capture the image** - Tap the camera button to take a photo
5. **Wait for analysis** - The app will process the image and analyze the nutrition information
6. **View results** - See the health rating and ingredient breakdown

## Architecture

The app follows modern Android development practices:

- **MVVM Architecture**: Clean separation of concerns with ViewModel
- **Jetpack Compose**: Modern declarative UI framework
- **Coroutines**: Asynchronous programming for smooth user experience
- **StateFlow**: Reactive state management
- **Retrofit**: HTTP client for API communication
- **CameraX**: Modern camera API
- **ML Kit**: On-device text recognition

## Project Structure

```
app/src/main/java/com/sgk/foodiq/
├── config/
│   └── ApiConfig.kt              # API configuration
├── data/
│   └── NutritionAnalysis.kt      # Data models
├── service/
│   ├── OCRService.kt             # Text extraction service
│   └── GeminiService.kt          # AI analysis service
├── ui/
│   ├── camera/
│   │   └── CameraScreen.kt       # Camera interface
│   └── results/
│       └── ResultsScreen.kt      # Results display
├── viewmodel/
│   └── FoodIQViewModel.kt        # State management
└── MainActivity.kt               # Main activity and navigation
```

## Dependencies

Key dependencies used in this project:

- **CameraX**: Camera functionality
- **ML Kit Text Recognition**: OCR capabilities
- **Retrofit + OkHttp**: Network communication
- **Gson**: JSON parsing
- **Jetpack Compose**: UI framework
- **Navigation Compose**: Screen navigation
- **Accompanist Permissions**: Permission handling

## Troubleshooting

### Common Issues

1. **"Please configure your Gemini API key"**
   - Make sure you've replaced the placeholder API key in `ApiConfig.kt`

2. **"No text detected in image"**
   - Ensure the nutrition label is clearly visible and well-lit
   - Try holding the camera steady and closer to the label

3. **"No internet connection"**
   - Check your device's internet connectivity
   - Ensure the app has internet permission

4. **Camera not working**
   - Grant camera permission when prompted
   - Check if your device has a working camera

### Performance Tips

- Use good lighting when capturing nutrition labels
- Hold the device steady for clearer images
- Ensure the entire nutrition label is visible in the frame
- Clean your camera lens for better image quality

## Future Enhancements

Potential improvements for future versions:

- Offline mode with cached analysis
- History of scanned products
- Barcode scanning integration
- Dietary restriction filters
- Nutritional goal tracking
- Social sharing features

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues, questions, or contributions, please open an issue on the GitHub repository.
