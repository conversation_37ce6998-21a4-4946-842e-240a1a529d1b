package com.sgk.foodiq.viewmodel

import android.graphics.Bitmap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sgk.foodiq.data.NutritionAnalysis
import com.sgk.foodiq.service.GeminiService
import com.sgk.foodiq.service.OCRService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

sealed class UiState {
    object Camera : UiState()
    object Processing : UiState()
    data class Results(val analysis: NutritionAnalysis) : UiState()
    data class Error(val message: String) : UiState()
}

class FoodIQViewModel : ViewModel() {
    
    private val ocrService = OCRService()
    private val geminiService = GeminiService()
    
    private val _uiState = MutableStateFlow<UiState>(UiState.Camera)
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()
    
    fun processImage(bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                _uiState.value = UiState.Processing
                
                // Step 1: Extract text using OCR
                val rawText = ocrService.extractTextFromImage(bitmap)
                val processedText = ocrService.processNutritionText(rawText)
                
                if (processedText.isBlank()) {
                    _uiState.value = UiState.Error("No nutrition information detected. Please try again with a clearer image.")
                    return@launch
                }
                
                // Step 2: Analyze with Gemini AI
                val analysis = geminiService.analyzeNutrition(processedText)
                
                _uiState.value = UiState.Results(analysis)
                
            } catch (e: Exception) {
                _uiState.value = UiState.Error("Failed to analyze image: ${e.message}")
            }
        }
    }
    
    fun resetToCamera() {
        _uiState.value = UiState.Camera
    }
    
    fun retryFromError() {
        _uiState.value = UiState.Camera
    }
}
