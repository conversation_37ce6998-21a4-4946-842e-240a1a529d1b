package com.sgk.foodiq.data

data class NutritionAnalysis(
    val rating: Int, // 1-10 scale
    val overallAssessment: String,
    val goodIngredients: List<IngredientAnalysis>,
    val badIngredients: List<IngredientAnalysis>,
    val neutralIngredients: List<IngredientAnalysis>
)

data class IngredientAnalysis(
    val name: String,
    val reason: String
)

data class GeminiRequest(
    val contents: List<Content>
)

data class Content(
    val parts: List<Part>
)

data class Part(
    val text: String
)

data class GeminiResponse(
    val candidates: List<Candidate>
)

data class Candidate(
    val content: Content
)
