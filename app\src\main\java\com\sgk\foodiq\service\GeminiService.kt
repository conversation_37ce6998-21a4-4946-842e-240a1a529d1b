package com.sgk.foodiq.service

import com.google.gson.Gson
import com.sgk.foodiq.config.ApiConfig
import com.sgk.foodiq.data.*
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query

interface GeminiApi {
    @POST("v1beta/models/gemini-1.5-flash-latest:generateContent")
    suspend fun generateContent(
        @Query("key") apiKey: String,
        @Body request: GeminiRequest
    ): GeminiResponse
}

class GeminiService {
    
    private val retrofit = Retrofit.Builder()
        .baseUrl("https://generativelanguage.googleapis.com/")
        .addConverterFactory(GsonConverterFactory.create())
        .build()
    
    private val api = retrofit.create(GeminiApi::class.java)
    private val gson = Gson()
    
    private val apiKey = ApiConfig.GEMINI_API_KEY
    
    suspend fun analyzeNutrition(nutritionText: String): NutritionAnalysis {
        if (apiKey == "YOUR_GEMINI_API_KEY_HERE") {
            throw Exception("Please configure your Gemini API key in ApiConfig.kt")
        }

        if (nutritionText.isBlank()) {
            throw Exception("No nutrition text provided for analysis")
        }

        val prompt = createNutritionAnalysisPrompt(nutritionText)

        val request = GeminiRequest(
            contents = listOf(
                Content(
                    parts = listOf(Part(text = prompt))
                )
            )
        )

        try {
            val response = api.generateContent(apiKey, request)

            if (response.candidates.isEmpty()) {
                throw Exception("No analysis received from Gemini API")
            }

            return parseNutritionAnalysis(response.candidates.first().content.parts.first().text)
        } catch (e: retrofit2.HttpException) {
            when (e.code()) {
                401 -> throw Exception("Invalid API key. Please check your Gemini API configuration.")
                429 -> throw Exception("API rate limit exceeded. Please try again later.")
                else -> throw Exception("Network error: ${e.message()}")
            }
        } catch (e: java.net.UnknownHostException) {
            throw Exception("No internet connection. Please check your network.")
        } catch (e: Exception) {
            throw Exception("Failed to analyze nutrition: ${e.message}")
        }
    }
    
    private fun createNutritionAnalysisPrompt(nutritionText: String): String {
        return """
            Analyze the following nutrition label text and provide a comprehensive health assessment.
            
            Nutrition Label Text:
            $nutritionText
            
            Please provide your analysis in the following JSON format:
            {
                "rating": [number from 1-10, where 10 is healthiest],
                "overallAssessment": "[brief overall health assessment]",
                "goodIngredients": [
                    {
                        "name": "[ingredient name]",
                        "reason": "[why it's good for health]"
                    }
                ],
                "badIngredients": [
                    {
                        "name": "[ingredient name]",
                        "reason": "[why it's concerning for health]"
                    }
                ],
                "neutralIngredients": [
                    {
                        "name": "[ingredient name]",
                        "reason": "[neutral impact explanation]"
                    }
                ]
            }
            
            Consider the following factors in your analysis:
            - Nutritional content (calories, fats, sugars, sodium, protein, fiber, vitamins)
            - Ingredient quality (natural vs artificial, preservatives, additives)
            - Processing level (whole foods vs highly processed)
            - Allergens and dietary restrictions
            - Overall health impact
            
            Provide specific, actionable insights about why each ingredient is categorized as good, bad, or neutral.
            Be concise but informative in your explanations.
            
            Return ONLY the JSON response, no additional text.
        """.trimIndent()
    }
    
    private fun parseNutritionAnalysis(responseText: String): NutritionAnalysis {
        return try {
            // Clean the response text to extract JSON
            val jsonText = responseText.trim()
                .removePrefix("```json")
                .removeSuffix("```")
                .trim()
            
            gson.fromJson(jsonText, NutritionAnalysis::class.java)
        } catch (e: Exception) {
            // Fallback analysis if parsing fails
            NutritionAnalysis(
                rating = 5,
                overallAssessment = "Unable to analyze nutrition label. Please try again with a clearer image.",
                goodIngredients = emptyList(),
                badIngredients = emptyList(),
                neutralIngredients = emptyList()
            )
        }
    }
}
