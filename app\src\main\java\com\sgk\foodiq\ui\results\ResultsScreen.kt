package com.sgk.foodiq.ui.results

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.sgk.foodiq.data.IngredientAnalysis
import com.sgk.foodiq.data.NutritionAnalysis

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ResultsScreen(
    analysis: NutritionAnalysis,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Nutrition Analysis") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                HealthRatingCard(rating = analysis.rating)
            }
            
            item {
                OverallAssessmentCard(assessment = analysis.overallAssessment)
            }
            
            if (analysis.goodIngredients.isNotEmpty()) {
                item {
                    Text(
                        text = "Good Ingredients",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF4CAF50)
                    )
                }
                items(analysis.goodIngredients) { ingredient ->
                    IngredientCard(
                        ingredient = ingredient,
                        type = IngredientType.GOOD
                    )
                }
            }
            
            if (analysis.badIngredients.isNotEmpty()) {
                item {
                    Text(
                        text = "Concerning Ingredients",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFF44336)
                    )
                }
                items(analysis.badIngredients) { ingredient ->
                    IngredientCard(
                        ingredient = ingredient,
                        type = IngredientType.BAD
                    )
                }
            }
            
            if (analysis.neutralIngredients.isNotEmpty()) {
                item {
                    Text(
                        text = "Neutral Ingredients",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF9E9E9E)
                    )
                }
                items(analysis.neutralIngredients) { ingredient ->
                    IngredientCard(
                        ingredient = ingredient,
                        type = IngredientType.NEUTRAL
                    )
                }
            }
        }
    }
}

@Composable
private fun HealthRatingCard(rating: Int) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = getRatingColor(rating).copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Health Rating",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(CircleShape)
                    .background(getRatingColor(rating)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "$rating",
                    style = MaterialTheme.typography.headlineLarge,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 32.sp
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "out of 10",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = getRatingDescription(rating),
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = getRatingColor(rating),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun OverallAssessmentCard(assessment: String) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Overall Assessment",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = assessment,
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = 20.sp
            )
        }
    }
}

@Composable
private fun IngredientCard(
    ingredient: IngredientAnalysis,
    type: IngredientType
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = type.color.copy(alpha = 0.05f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            Icon(
                imageVector = type.icon,
                contentDescription = null,
                tint = type.color,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = ingredient.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    color = type.color
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = ingredient.reason,
                    style = MaterialTheme.typography.bodySmall,
                    lineHeight = 16.sp
                )
            }
        }
    }
}

private enum class IngredientType(
    val color: Color,
    val icon: ImageVector
) {
    GOOD(Color(0xFF4CAF50), Icons.Default.CheckCircle),
    BAD(Color(0xFFF44336), Icons.Default.Error),
    NEUTRAL(Color(0xFF9E9E9E), Icons.Default.Info)
}

private fun getRatingColor(rating: Int): Color {
    return when {
        rating >= 8 -> Color(0xFF4CAF50) // Green
        rating >= 6 -> Color(0xFFFF9800) // Orange
        rating >= 4 -> Color(0xFFFF5722) // Deep Orange
        else -> Color(0xFFF44336) // Red
    }
}

private fun getRatingDescription(rating: Int): String {
    return when {
        rating >= 8 -> "Excellent Choice!"
        rating >= 6 -> "Good Option"
        rating >= 4 -> "Moderate Choice"
        else -> "Consider Alternatives"
    }
}
