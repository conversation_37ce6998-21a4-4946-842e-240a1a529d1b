package com.sgk.foodiq

import com.sgk.foodiq.service.OCRService
import org.junit.Test
import org.junit.Assert.*

class OCRServiceTest {

    private val ocrService = OCRService()

    @Test
    fun testProcessNutritionText_filtersRelevantContent() {
        val rawText = """
            Product Name: Test Cereal
            Nutrition Facts
            Serving Size: 1 cup (30g)
            Calories: 150
            Total Fat: 2g
            Sodium: 200mg
            Total Carbohydrate: 30g
            Dietary Fiber: 5g
            Total Sugars: 8g
            Protein: 4g
            Vitamin D: 2mcg
            Calcium: 100mg
            Iron: 8mg
            
            Ingredients: Whole grain oats, sugar, salt, natural flavor
            
            Random text that shouldn't be included
            Some other non-nutrition text
        """.trimIndent()

        val processedText = ocrService.processNutritionText(rawText)
        
        // Should contain nutrition-related content
        assertTrue(processedText.contains("Nutrition Facts"))
        assertTrue(processedText.contains("Calories: 150"))
        assertTrue(processedText.contains("Total Fat: 2g"))
        assertTrue(processedText.contains("Ingredients:"))
        
        // Should filter out irrelevant content
        assertFalse(processedText.contains("Random text that shouldn't be included"))
        assertFalse(processedText.contains("Some other non-nutrition text"))
    }

    @Test
    fun testProcessNutritionText_handlesEmptyInput() {
        val result = ocrService.processNutritionText("")
        assertEquals("", result)
    }

    @Test
    fun testProcessNutritionText_handlesNutritionKeywords() {
        val rawText = """
            Calories 200
            Fat 5g
            Sodium 300mg
            Carbohydrate 40g
            Protein 8g
        """.trimIndent()

        val processedText = ocrService.processNutritionText(rawText)
        
        assertTrue(processedText.contains("Calories 200"))
        assertTrue(processedText.contains("Fat 5g"))
        assertTrue(processedText.contains("Sodium 300mg"))
    }
}
